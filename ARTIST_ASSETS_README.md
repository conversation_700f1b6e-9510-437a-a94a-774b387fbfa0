# Artist Assets Management

This document explains how to manage artist assets (images, press kits, and mixtapes) for the dynamic artist pages.

## Folder Structure

The following folder structure has been created in the `public` directory:

```
public/
├── artists/                    # Main artist profile images
│   ├── jaywall/               # Individual artist image folders
│   ├── arrow/
│   ├── snezzy/
│   ├── artic/
│   ├── bcode/
│   └── bonuzz/
├── press-kits/                # Press kit PDF files
└── mixtapes/                  # Audio files organized by artist
    ├── jaywall/
    ├── arrow/
    ├── snezzy/
    ├── artic/
    ├── bcode/
    └── bonuzz/
```

## How to Add Assets

### 1. Artist Images

**Main Profile Image:**
- Already exists in `/public/artists/` (e.g., `jaywallbe.jpg`)
- Used in the hero section and as the main artist image

**Additional Images (Future Enhancement):**
- Place additional images in the artist's individual folder
- Example: `/public/artists/jaywall/performance1.jpg`
- The `getArtistImages()` function can be extended to load multiple images

### 2. Press Kits

**Location:** `/public/press-kits/`
**Naming Convention:** `{artistname}-presskit.pdf`

Examples:
- `/public/press-kits/jaywall-presskit.pdf`
- `/public/press-kits/arrow-presskit.pdf`

**Content should include:**
- High-resolution photos
- Artist biography
- Technical rider
- Contact information
- Performance history

### 3. Mixtapes/Audio Files

**Location:** `/public/mixtapes/{artistname}/`
**Supported Formats:** MP3, WAV, M4A

Examples:
- `/public/mixtapes/jaywall/dnb-essentials-2024.mp3`
- `/public/mixtapes/arrow/urban-classics.mp3`

## Updating Artist Data

Artist information is stored in `/src/data/artists.json`. To add or modify artist data:

### Adding a New Artist

1. Add a new artist object to the `artists` array in `artists.json`
2. Create the corresponding folders in `public/`
3. Add the artist's assets to the appropriate folders

### Modifying Existing Artist Data

Edit the artist object in `artists.json`. Key fields:

- `id`: Unique identifier (used in URLs)
- `name`: Artist display name
- `image`: Path to main profile image
- `genres`: Array of music genres
- `imageFolder`: Folder name for additional images
- `about`: Artist biography/description
- `location`: Artist's base location
- `experience`: Years of experience
- `totalShows`: Number of shows performed
- `careerHighlights`: Array of achievements
- `pressKit`: Path to press kit PDF
- `mixtapes`: Array of mixtape objects with title, duration, and file path

## File Size Recommendations

### Images
- **Profile Images:** Max 2MB, recommended 1200x1600px (3:4 aspect ratio)
- **Additional Images:** Max 1MB each, recommended 800x600px

### Press Kits
- **PDF Files:** Max 10MB, include high-quality images and comprehensive information

### Audio Files
- **Mixtapes:** Max 50MB per file, 128-320 kbps MP3 recommended

## SEO Considerations

- Use descriptive filenames (e.g., `jaywall-tomorrowland-2023.jpg`)
- Optimize images for web (compress without losing quality)
- Include alt text in the JSON data for accessibility

## Future Enhancements

### Multiple Images per Artist
The `getArtistImages()` function in `/src/utils/artistData.ts` can be extended to:
- Scan the artist's image folder
- Return an array of all images
- Support image galleries in the artist page

### Audio Player Integration
- Implement a proper audio player component
- Add streaming capabilities
- Include waveform visualization

### Dynamic Asset Loading
- Implement lazy loading for images
- Add loading states for audio files
- Cache management for better performance

## Database Migration (Future)

When moving from JSON to a database:

1. **Database Schema:**
   - Artists table with all current JSON fields
   - Separate tables for mixtapes and career highlights
   - File paths stored as URLs or relative paths

2. **File Storage:**
   - Consider cloud storage (AWS S3, Cloudinary)
   - Implement CDN for better performance
   - Add file upload functionality for admin panel

3. **API Endpoints:**
   - GET /api/artists - List all artists
   - GET /api/artists/:id - Get specific artist
   - POST/PUT/DELETE for admin operations

## Troubleshooting

### Common Issues

1. **Images not loading:**
   - Check file paths in JSON match actual file locations
   - Ensure files are in the `public` directory
   - Verify file extensions match (case-sensitive)

2. **Press kit downloads not working:**
   - Confirm PDF files exist in `/public/press-kits/`
   - Check browser popup blockers
   - Verify file permissions

3. **Audio files not playing:**
   - Ensure audio files are in supported formats
   - Check file size limits
   - Verify MIME types are properly configured

### Development Tips

- Use browser developer tools to check network requests
- Test with different file sizes and formats
- Implement error handling for missing files
- Add loading states for better UX

## Contact

For questions about asset management or technical implementation, contact the development team.
