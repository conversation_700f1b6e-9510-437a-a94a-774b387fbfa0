# Artist Images Directory

This directory contains artist images organized by individual folders.

## Structure
```
artists/
├── [main profile images]     # Current profile images
├── jaywall/                  # Additional images for <PERSON> <PERSON>
├── arrow/                    # Additional images for <PERSON> <PERSON>  
├── snezzy/                   # Additional images for <PERSON> Snezzy
├── artic/                    # Additional images for DJ Artic
├── bcode/                    # Additional images for <PERSON> <PERSON><PERSON><PERSON>
└── bonuzz/                   # Additional images for <PERSON> Bonuzz
```

## Image Guidelines

### Main Profile Images
- **Location:** Root of `/public/artists/`
- **Format:** JPG, PNG, WebP
- **Size:** Max 2MB
- **Dimensions:** 1200x1600px (3:4 aspect ratio recommended)
- **Usage:** Hero section, main artist display

### Additional Images (Individual Folders)
- **Location:** `/public/artists/{artistname}/`
- **Format:** JPG, PNG, WebP  
- **Size:** Max 1MB each
- **Dimensions:** 800x600px recommended
- **Usage:** Image galleries, carousel displays

## Current Profile Images
- jaywallbe.jpg - DJ <PERSON>
- dj_arrow_official.jpg - DJ <PERSON>
- snezzy_official_.jpg - DJ Snezzy
- deejay_artic.jpg - DJ Artic
- dj.bcode.jpg - DJ B-Code
- djbonuzz.jpg - DJ Bonuzz

## Future Enhancement
The `getArtistImages()` function in `/src/utils/artistData.ts` can be extended to automatically load all images from an artist's individual folder for gallery displays.
