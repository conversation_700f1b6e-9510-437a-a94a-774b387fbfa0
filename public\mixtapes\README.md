# Mixtapes Directory

This directory contains audio files (mixtapes) organized by artist.

## Structure
```
mixtapes/
├── jaywall/
├── arrow/
├── snezzy/
├── artic/
├── bcode/
└── bonuzz/
```

## File Guidelines
- **Formats:** MP3, WAV, M4A
- **Quality:** 128-320 kbps for MP3
- **Size:** Maximum 50MB per file
- **Naming:** Descriptive names (e.g., `dnb-essentials-2024.mp3`)

## Current Mixtapes Structure

### DJ <PERSON> (/jaywall/)
- dnb-essentials-2024.mp3
- festival-vibes.mp3

### DJ <PERSON> (/arrow/)
- urban-classics.mp3
- rnb-smooth.mp3

### DJ Snezzy (/snezzy/)
- underground-journey.mp3
- house-foundations.mp3

### DJ Artic (/artic/)
- commercial-hits.mp3
- wedding-classics.mp3

### DJ B-Code (/bcode/)
- allround-excellence.mp3
- corporate-vibes.mp3

### DJ <PERSON>z (/bonuzz/)
- party-starters.mp3
- feel-good-classics.mp3

## Adding New Mixtapes
1. Place audio file in the appropriate artist folder
2. Update the artist's data in `/src/data/artists.json`
3. Add mixtape object with title, duration, and file path
