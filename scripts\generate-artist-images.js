import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuration
const PROJECT_ROOT = path.dirname(__dirname);
const ARTISTS_JSON_PATH = path.join(PROJECT_ROOT, 'src/data/artists.json');
const PUBLIC_ARTISTS_PATH = path.join(PROJECT_ROOT, 'public/artists');
const SUPPORTED_EXTENSIONS = ['.jpg', '.jpeg', '.png', '.webp'];

/**
 * Get all image files from a directory
 */
function getImagesFromDirectory(dirPath) {
  try {
    if (!fs.existsSync(dirPath)) {
      console.warn(`Directory does not exist: ${dirPath}`);
      return [];
    }

    const files = fs.readdirSync(dirPath);
    return files
      .filter(file => {
        const ext = path.extname(file).toLowerCase();
        return SUPPORTED_EXTENSIONS.includes(ext);
      })
      .map(file => `/artists/${path.basename(dirPath)}/${file}`)
      .sort(); // Sort for consistent ordering
  } catch (error) {
    console.error(`Error reading directory ${dirPath}:`, error.message);
    return [];
  }
}

/**
 * Update artists.json with dynamically discovered images
 */
function updateArtistsWithImages() {
  try {
    // Read current artists.json
    const artistsData = JSON.parse(fs.readFileSync(ARTISTS_JSON_PATH, 'utf8'));
    
    let updated = false;

    // Process each artist
    artistsData.artists.forEach(artist => {
      const artistFolderPath = path.join(PUBLIC_ARTISTS_PATH, artist.imageFolder);
      const discoveredImages = getImagesFromDirectory(artistFolderPath);
      
      // Only update if images were found and are different from current
      if (discoveredImages.length > 0) {
        const currentImages = artist.additionalImages || [];
        
        // Check if arrays are different
        const imagesChanged = JSON.stringify(currentImages.sort()) !== JSON.stringify(discoveredImages.sort());
        
        if (imagesChanged) {
          artist.additionalImages = discoveredImages;
          updated = true;
          console.log(`Updated ${artist.name}: Found ${discoveredImages.length} images`);
          discoveredImages.forEach(img => console.log(`  - ${img}`));
        } else {
          console.log(`${artist.name}: No changes (${discoveredImages.length} images)`);
        }
      } else {
        // No images found, ensure empty array
        if (!artist.additionalImages || artist.additionalImages.length > 0) {
          artist.additionalImages = [];
          updated = true;
          console.log(`${artist.name}: No images found, cleared additionalImages`);
        }
      }
    });

    // Write back to file if updated
    if (updated) {
      fs.writeFileSync(ARTISTS_JSON_PATH, JSON.stringify(artistsData, null, 2));
      console.log('\n✅ artists.json updated successfully!');
    } else {
      console.log('\n✅ No updates needed - all images are up to date');
    }

  } catch (error) {
    console.error('Error updating artists.json:', error.message);
    process.exit(1);
  }
}

/**
 * Create example images for demonstration
 */
function createExampleImages() {
  const artistsData = JSON.parse(fs.readFileSync(ARTISTS_JSON_PATH, 'utf8'));
  
  artistsData.artists.forEach(artist => {
    const artistDir = path.join(PUBLIC_ARTISTS_PATH, artist.imageFolder);
    
    // Create directory if it doesn't exist
    if (!fs.existsSync(artistDir)) {
      fs.mkdirSync(artistDir, { recursive: true });
      console.log(`Created directory: ${artistDir}`);
    }
    
    // Create placeholder files (you can replace these with actual images)
    const placeholderImages = [
      'performance.jpg',
      'studio.jpg',
      'backstage.jpg'
    ];
    
    placeholderImages.forEach(imageName => {
      const imagePath = path.join(artistDir, imageName);
      if (!fs.existsSync(imagePath)) {
        // Create empty file as placeholder
        fs.writeFileSync(imagePath, '');
        console.log(`Created placeholder: ${imagePath}`);
      }
    });
  });
}

// Main execution
function main() {
  const command = process.argv[2];
  
  switch (command) {
    case 'update':
      console.log('🔍 Scanning artist folders for images...\n');
      updateArtistsWithImages();
      break;
      
    case 'create-examples':
      console.log('📁 Creating example image placeholders...\n');
      createExampleImages();
      break;
      
    case 'full':
      console.log('📁 Creating example folders and updating images...\n');
      createExampleImages();
      updateArtistsWithImages();
      break;
      
    default:
      console.log(`
Usage: node scripts/generate-artist-images.js [command]

Commands:
  update          - Scan existing folders and update artists.json
  create-examples - Create placeholder image files for testing
  full           - Create examples and update (recommended for first run)

Examples:
  npm run update-artist-images
  node scripts/generate-artist-images.js update
  node scripts/generate-artist-images.js full
      `);
  }
}

main();
