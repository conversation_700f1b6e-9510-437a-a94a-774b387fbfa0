# Adding Images - Step by Step Example

## Quick Start: Adding Images to an Artist

Let's say you want to add new images for **DJ <PERSON>**:

### Step 1: Add Images to Folder
```bash
# Navigate to the artist's folder
cd public/artists/jaywall/

# Add your images (any of these formats work)
# - concert-photo.jpg
# - backstage-moment.png  
# - studio-session.webp
# - festival-performance.jpeg
```

### Step 2: Update the System
```bash
# Option A: Update images only
npm run update-artist-images

# Option B: Build (automatically updates images first)
npm run build

# Option C: Dev mode (images update on next build)
npm run dev
```

### Step 3: That's It! 🎉
The images will automatically appear in the artist's page carousel.

## What Happens Behind the Scenes

1. **<PERSON>ript scans** `/public/artists/jaywall/` folder
2. **Finds all images** with supported extensions
3. **Updates** `src/data/artists.json` automatically
4. **Sorts images** alphabetically for consistent ordering
5. **Artist page** displays them in the carousel

## Example Output

After adding 3 new images to DJ <PERSON>'s folder:

```bash
$ npm run update-artist-images

🔍 Scanning artist folders for images...

Updated DJ Jay <PERSON>: Found 10 images
  - /artists/jaywall/_DSC6723.jpg
  - /artists/jaywall/_DSC6918.jpg
  - /artists/jaywall/backstage.jpg
  - /artists/jaywall/concert-photo.jpg      # ← NEW
  - /artists/jaywall/festival-performance.jpeg # ← NEW  
  - /artists/jaywall/jaywallhypeodream-18.jpg
  - /artists/jaywall/jaywallhypeodream-4.jpg
  - /artists/jaywall/performance.jpg
  - /artists/jaywall/studio-session.webp    # ← NEW
  - /artists/jaywall/studio.jpg

✅ artists.json updated successfully!
```

## Adding a New Artist

### Step 1: Update JSON
Add the new artist to `src/data/artists.json`:

```json
{
  "id": "7",
  "name": "DJ NewArtist", 
  "image": "/artists/newartist-main.jpg",
  "imageFolder": "newartist",
  "additionalImages": [],  // ← Will be auto-populated
  // ... other fields
}
```

### Step 2: Create Folder & Add Images
```bash
mkdir public/artists/newartist
# Add images to the folder
```

### Step 3: Run Update
```bash
npm run update-artist-images
```

The `additionalImages` array will be automatically populated!

## Pro Tips

### 🎯 Image Naming Best Practices
- Use descriptive names: `festival-crowd.jpg` instead of `IMG_001.jpg`
- Use hyphens for spaces: `studio-session.jpg`
- Keep names short but meaningful
- Avoid special characters

### 📁 Folder Organization
```
public/artists/jaywall/
├── performance-tomorrowland.jpg
├── studio-recording.jpg  
├── backstage-interview.jpg
├── crowd-interaction.jpg
└── equipment-setup.jpg
```

### 🔄 Automatic Build Integration
The build process automatically updates images, so your deployment pipeline stays clean:

```bash
# This automatically scans for new images before building
npm run build

# Perfect for CI/CD - no manual steps needed!
```

### 🚫 What Gets Ignored
- Non-image files (`.txt`, `.pdf`, etc.)
- Hidden files (starting with `.`)
- Unsupported formats (`.gif`, `.svg`, etc.)
- The main profile image (it's handled separately)

## Troubleshooting

### Images Not Showing Up?
1. Check file extensions are supported (jpg, jpeg, png, webp)
2. Ensure files are in the correct folder: `/public/artists/{artistname}/`
3. Run `npm run update-artist-images` manually
4. Check the console output for any errors

### Build Failing?
1. Make sure the `scripts/` folder exists
2. Check that Node.js can read the artist folders
3. Verify `src/data/artists.json` is valid JSON

### Need to Reset?
```bash
# This will recreate placeholder images and update everything
npm run setup-artist-images
```
