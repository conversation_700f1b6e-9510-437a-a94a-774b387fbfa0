import React from 'react';
import Header from '@/components/Header';
import CTAStrip from '@/components/CTAStrip';
import Footer from '@/components/Footer';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { hero<PERSON>ontainer, heroTitle, heroSubtitle, staggerContainer, staggerItem, getReducedMotionVariants } from '@/lib/animations';
import { AnimatedSection, StaggeredGrid, PageTransition } from '@/components/animations';
import { useTranslation } from 'react-i18next';
import SEO from '@/components/SEO';
import { useSEO } from '@/hooks/useSEO';

const About = () => {
  const { t } = useTranslation('pages');
  const { getPageSEO, getBreadcrumbStructuredData, getCurrentBreadcrumbs } = useSEO();
  const aboutSEO = getPageSEO('about');

  const teamMembers = [
    {
      name: "<PERSON>",
      role: "Co-founder",
      image: "/team/leon.jpg",
      email: "<EMAIL>",
      phone: "+32 472 97 21 06"
    },
    {
      name: "<PERSON>ek<PERSON>",
      role: "Co-founder",
      image: "/team/thomas.jpg",
      email: "<EMAIL>",
      phone: "+32 472 38 13 58"
    },
    {
      name: "Warre Lejaeghere",
      role: "Artist management partner",
      image: "/team/warre.jpg",
      email: "<EMAIL>",
      phone: "+32 471 22 99 34"
    }
  ];

  return (
    <>
      <SEO
        title={aboutSEO.title}
        description={aboutSEO.description}
        keywords={aboutSEO.keywords}
        structuredData={getBreadcrumbStructuredData(getCurrentBreadcrumbs())}
      />
      <div className="min-h-screen flex flex-col bg-background text-foreground relative overflow-hidden">
        {/* Cosmic particle effect (background dots) */}
        <div className="absolute inset-0 cosmic-grid opacity-30"></div>

        {/* Gradient glow effect */}
        <div className="absolute top-[450px] left-1/2 -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] rounded-full">
          <div className="w-full h-full opacity-10 bg-primary blur-[120px]"></div>
        </div>

        <div className="relative z-10 flex flex-col min-h-screen">
        <Header />
        <PageTransition>
          <main className="flex-1">
          {/* Hero Section */}
          <AnimatedSection className="relative w-full py-20 md:py-28 px-6 md:px-12 flex flex-col items-center justify-center">
            <motion.div
              className="relative z-10 max-w-5xl text-center space-y-8"
              variants={getReducedMotionVariants(heroContainer)}
              initial="hidden"
              animate="visible"
            >
              <motion.div
                className="flex justify-center"
                variants={getReducedMotionVariants(staggerItem)}
              >
                <span className="border border-muted inline-flex items-center gap-2 px-3 py-1 text-xs font-medium rounded-full text-primary transition-colors cursor-pointer">
                  Our Story
                </span>
              </motion.div>

              <motion.h1
                className="text-4xl md:text-6xl lg:text-7xl font-semibold text-balance text-foreground"
                variants={getReducedMotionVariants(heroTitle)}
              >
                {t('about.title')}
              </motion.h1>

              <motion.p
                className="text-xl md:text-2xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
                variants={getReducedMotionVariants(heroSubtitle)}
              >
                {t('about.subtitle')}
              </motion.p>
            </motion.div>
          </AnimatedSection>

        {/* Mission Section */}
        <AnimatedSection className="py-16 px-6 md:px-12">
          <motion.div
            className="max-w-4xl mx-auto text-center space-y-8"
            variants={getReducedMotionVariants(staggerContainer)}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-50px" }}
          >
            <motion.h2
              className="text-3xl md:text-4xl font-semibold"
              variants={getReducedMotionVariants(staggerItem)}
            >
              Our team
            </motion.h2>
            <motion.p
              className="text-lg text-muted-foreground leading-relaxed"
              variants={getReducedMotionVariants(staggerItem)}
            >
              Founded in 2014, our agency emerged from a shared vision to bridge the gap between exceptional electronic music talent and the global stage. We believe that great music transcends boundaries, and our mission is to provide artists with the tools, connections, and strategic guidance they need to reach audiences worldwide.
            </motion.p>
          </motion.div>
        </AnimatedSection>

        {/* Team Section */}
        <AnimatedSection className="py-16 px-6 md:px-12">
          <div className="max-w-6xl mx-auto">
            <StaggeredGrid className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-6">
              {teamMembers.map((member, index) => (
                <motion.div
                  key={index}
                  whileHover={{ scale: 1.005, y: -2 }}
                  whileTap={{ scale: 0.995 }}
                >
                  <Card className="border-border bg-background hover:border-primary/20 transition-all duration-300 overflow-hidden h-full rounded-xl">
                    <div className="aspect-square overflow-hidden">
                      <img
                        src={member.image}
                        alt={member.name}
                        className="w-full h-full object-cover"
                      />
                    </div>
                    <CardContent className="p-8 text-left">
                      <h3 className="text-2xl font-semibold">{member.name}</h3>
                      <p className="mb-4 mt-1 font-medium">{member.role}</p>
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <p>{member.email}</p>
                        <p>{member.phone}</p>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </StaggeredGrid>
          </div>
        </AnimatedSection>

        {/* Company Section */}
        <AnimatedSection className="py-20 px-6 md:px-12 bg-background mb-16">
          <motion.div
            className="max-w-4xl mx-auto text-center"
            variants={getReducedMotionVariants(staggerContainer)}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true, margin: "-50px" }}
          >
            <motion.h2
              className="text-3xl md:text-4xl font-semibold text-foreground mb-6"
              variants={getReducedMotionVariants(staggerItem)}
            >
              Our company
            </motion.h2>
            <motion.p
              className="text-lg text-muted-foreground mb-10 max-w-2xl mx-auto"
              variants={getReducedMotionVariants(staggerItem)}
            >
              [text over CE en SC] With over a decade of experience in the electronic music industry, we've built lasting relationships
              with artists, venues, and event organizers worldwide. Let us help you create unforgettable experiences
              that resonate with your audience and leave a lasting impact.
            </motion.p>
            <motion.div
              className="flex justify-center pt-4 gap-4"
              variants={getReducedMotionVariants(staggerItem)}
            >
              <Link to="/contact">
                <Button className="group inline-flex items-center gap-2 bg-primary hover:bg-primary/90 text-primary-foreground px-6 py-3 rounded-full">
                  <span>Contact us</span>
                  <svg
                    className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Button>
              </Link>
              <Link to="https://www.stagecloud.be">
                <Button variant="outline" className="group inline-flex items-center gap-2 px-6 py-3 rounded-full">
                  <span>Discover StageCloud</span>
                  <svg
                    className="w-4 h-4 transition-transform duration-200 group-hover:translate-x-1"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </Button>
              </Link>
            </motion.div>
          </motion.div>
        </AnimatedSection>
          </main>
          <CTAStrip />
          <Footer />
        </PageTransition>
        </div>
      </div>
    </>
  );
};

export default About;