import artistsData from '@/data/artists.json';

export interface Mixtape {
  title: string;
  duration: string;
  file: string;
}

export interface Artist {
  id: string;
  name: string;
  image: string;
  genres: string[];
  imageFolder: string;
  about: string;
  location: string;
  experience: string;
  totalShows: number;
  careerHighlights: string[];
  pressKit: string;
  mixtapes: Mixtape[];
}

export interface ArtistsData {
  artists: Artist[];
}

// Load artists data
export const getArtists = (): Artist[] => {
  return (artistsData as ArtistsData).artists;
};

// Get a specific artist by ID
export const getArtistById = (id: string): Artist | undefined => {
  return getArtists().find(artist => artist.id === id);
};

// Get all unique genres
export const getGenres = (): string[] => {
  const allGenres = getArtists().flatMap(artist => artist.genres);
  const uniqueGenres = Array.from(new Set(allGenres));
  return ['All', ...uniqueGenres.sort()];
};

// Filter artists by genre
export const getArtistsByGenre = (genre: string): Artist[] => {
  if (genre === 'All') {
    return getArtists();
  }
  return getArtists().filter(artist => artist.genres.includes(genre));
};

// Get artist images from their folder
export const getArtistImages = (artist: Artist): string[] => {
  // For now, return the main image. In the future, you can scan the folder
  // or maintain a list of images for each artist
  return [artist.image];
};
