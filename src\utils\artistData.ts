import artistsData from '@/data/artists.json';

export interface Mixtape {
  title: string;
  duration: string;
  file: string;
}

export interface Artist {
  id: string;
  name: string;
  image: string;
  genres: string[];
  imageFolder: string;
  additionalImages: string[];
  about: string;
  location: string;
  experience: string;
  totalShows: number;
  careerHighlights: string[];
  pressKit: string;
  mixtapes: Mixtape[];
}

export interface ArtistsData {
  artists: Artist[];
}

// Load artists data
export const getArtists = (): Artist[] => {
  return (artistsData as ArtistsData).artists;
};

// Get a specific artist by ID
export const getArtistById = (id: string): Artist | undefined => {
  return getArtists().find(artist => artist.id === id);
};

// Get all unique genres
export const getGenres = (): string[] => {
  const allGenres = getArtists().flatMap(artist => artist.genres);
  const uniqueGenres = Array.from(new Set(allGenres));
  return ['All', ...uniqueGenres.sort()];
};

// Filter artists by genre
export const getArtistsByGenre = (genre: string): Artist[] => {
  if (genre === 'All') {
    return getArtists();
  }
  return getArtists().filter(artist => artist.genres.includes(genre));
};

// Get artist images from their folder (excluding main profile image)
export const getArtistImages = (artist: Artist): string[] => {
  // Return only the additional images from the artist's specific folder
  // This excludes the main profile image which is displayed separately
  return artist.additionalImages || [];
};

// Get all images including the main profile image (for future use)
export const getAllArtistImages = (artist: Artist): string[] => {
  return [artist.image, ...artist.additionalImages];
};
